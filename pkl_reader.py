#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PKL文件读取工具
支持读取、显示和分析pickle文件内容
"""

import pickle
import os
import sys
from pathlib import Path
from typing import Any, Optional
import argparse


class PKLReader:
    """PKL文件读取器类"""
    
    def __init__(self):
        self.data = None
        self.file_path = None
    
    def read_pkl(self, file_path: str) -> Any:
        """
        读取pkl文件
        
        Args:
            file_path (str): pkl文件路径
            
        Returns:
            Any: 反序列化后的数据
            
        Raises:
            FileNotFoundError: 文件不存在
            pickle.UnpicklingError: 反序列化失败
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        if not file_path.suffix.lower() in ['.pkl', '.pickle']:
            print(f"警告: 文件扩展名不是.pkl或.pickle: {file_path}")
        
        try:
            with open(file_path, 'rb') as f:
                self.data = pickle.load(f)
                self.file_path = file_path
                print(f"✅ 成功读取文件: {file_path}")
                return self.data
        except pickle.UnpicklingError as e:
            raise pickle.UnpicklingError(f"反序列化失败: {e}")
        except Exception as e:
            raise Exception(f"读取文件时发生错误: {e}")
    
    def get_data_info(self) -> dict:
        """
        获取数据的基本信息
        
        Returns:
            dict: 包含数据类型、大小等信息的字典
        """
        if self.data is None:
            return {"error": "没有加载数据"}
        
        info = {
            "数据类型": type(self.data).__name__,
            "文件路径": str(self.file_path),
            "文件大小": f"{self.file_path.stat().st_size} 字节" if self.file_path else "未知"
        }
        
        # 根据数据类型添加更多信息
        if isinstance(self.data, (list, tuple)):
            info["元素数量"] = len(self.data)
            if len(self.data) > 0:
                info["第一个元素类型"] = type(self.data[0]).__name__
        elif isinstance(self.data, dict):
            info["键数量"] = len(self.data)
            if self.data:
                info["键类型示例"] = type(list(self.data.keys())[0]).__name__
                info["值类型示例"] = type(list(self.data.values())[0]).__name__
        elif isinstance(self.data, str):
            info["字符串长度"] = len(self.data)
        elif hasattr(self.data, '__len__'):
            try:
                info["长度"] = len(self.data)
            except:
                pass
        
        return info
    
    def display_data(self, max_items: int = 10, max_depth: int = 2) -> None:
        """
        显示数据内容
        
        Args:
            max_items (int): 最大显示项目数
            max_depth (int): 最大显示深度
        """
        if self.data is None:
            print("❌ 没有加载数据")
            return
        
        print("\n" + "="*50)
        print("📊 数据内容预览:")
        print("="*50)
        
        self._display_recursive(self.data, max_items, max_depth, 0)
    
    def _display_recursive(self, obj: Any, max_items: int, max_depth: int, current_depth: int) -> None:
        """递归显示数据内容"""
        indent = "  " * current_depth
        
        if current_depth >= max_depth:
            print(f"{indent}... (达到最大深度)")
            return
        
        if isinstance(obj, dict):
            print(f"{indent}字典 (共{len(obj)}个键):")
            for i, (key, value) in enumerate(obj.items()):
                if i >= max_items:
                    print(f"{indent}  ... (还有{len(obj)-max_items}个项目)")
                    break
                print(f"{indent}  {key}: ", end="")
                if isinstance(value, (dict, list, tuple)) and current_depth < max_depth - 1:
                    print()
                    self._display_recursive(value, max_items, max_depth, current_depth + 2)
                else:
                    print(f"{type(value).__name__} = {str(value)[:100]}{'...' if len(str(value)) > 100 else ''}")
        
        elif isinstance(obj, (list, tuple)):
            type_name = "列表" if isinstance(obj, list) else "元组"
            print(f"{indent}{type_name} (共{len(obj)}个元素):")
            for i, item in enumerate(obj):
                if i >= max_items:
                    print(f"{indent}  ... (还有{len(obj)-max_items}个元素)")
                    break
                print(f"{indent}  [{i}]: ", end="")
                if isinstance(item, (dict, list, tuple)) and current_depth < max_depth - 1:
                    print()
                    self._display_recursive(item, max_items, max_depth, current_depth + 2)
                else:
                    print(f"{type(item).__name__} = {str(item)[:100]}{'...' if len(str(item)) > 100 else ''}")
        
        else:
            print(f"{indent}{type(obj).__name__}: {str(obj)[:200]}{'...' if len(str(obj)) > 200 else ''}")
    
    def save_as_text(self, output_path: str) -> None:
        """
        将数据保存为文本文件
        
        Args:
            output_path (str): 输出文件路径
        """
        if self.data is None:
            print("❌ 没有加载数据")
            return
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(f"PKL文件内容导出\n")
                f.write(f"原文件: {self.file_path}\n")
                f.write(f"数据类型: {type(self.data).__name__}\n")
                f.write("="*50 + "\n\n")
                f.write(str(self.data))
            print(f"✅ 数据已保存到: {output_path}")
        except Exception as e:
            print(f"❌ 保存失败: {e}")


def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(description='PKL文件读取工具')
    parser.add_argument('file_path', help='PKL文件路径')
    parser.add_argument('--info', '-i', action='store_true', help='只显示文件信息')
    parser.add_argument('--max-items', '-m', type=int, default=10, help='最大显示项目数 (默认: 10)')
    parser.add_argument('--max-depth', '-d', type=int, default=2, help='最大显示深度 (默认: 2)')
    parser.add_argument('--save-text', '-s', help='保存为文本文件的路径')
    
    args = parser.parse_args()
    
    # 创建读取器实例
    reader = PKLReader()
    
    try:
        # 读取pkl文件
        data = reader.read_pkl(args.file_path)
        
        # 显示基本信息
        info = reader.get_data_info()
        print("\n📋 文件信息:")
        print("-" * 30)
        for key, value in info.items():
            print(f"{key}: {value}")
        
        # 如果不是只显示信息模式，则显示数据内容
        if not args.info:
            reader.display_data(args.max_items, args.max_depth)
        
        # 如果指定了保存路径，则保存为文本文件
        if args.save_text:
            reader.save_as_text(args.save_text)
            
    except Exception as e:
        print(f"❌ 错误: {e}")
        sys.exit(1)


# 简单的使用示例函数
def example_usage():
    """使用示例"""
    print("PKL文件读取器使用示例:")
    print("1. 基本用法:")
    print("   reader = PKLReader()")
    print("   data = reader.read_pkl('data.pkl')")
    print("   reader.display_data()")
    print()
    print("2. 命令行用法:")
    print("   python pkl_reader.py data.pkl")
    print("   python pkl_reader.py data.pkl --info")
    print("   python pkl_reader.py data.pkl --max-items 5 --max-depth 3")
    print("   python pkl_reader.py data.pkl --save-text output.txt")


if __name__ == "__main__":
    if len(sys.argv) == 1:
        example_usage()
    else:
        main()
