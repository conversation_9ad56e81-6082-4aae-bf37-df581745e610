#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PKL文件读取工具
支持读取、显示和分析pickle文件内容
专门优化用于处理Question和ExamQuestions模型数据
"""

import pickle
import os
import sys
from pathlib import Path
from typing import Any, Optional
import argparse
import json

# 尝试导入本地模型并设置模块路径
try:
    import sys
    import os

    # 确保当前目录在Python路径中
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

    # 尝试多种导入方式
    try:
        from model import Question, ExamQuestions
        MODELS_AVAILABLE = True
    except ImportError:
        # 如果pkl文件中引用了model.model，我们需要创建这个模块别名
        import model
        sys.modules['model.model'] = model
        from model import Question, ExamQuestions
        MODELS_AVAILABLE = True

except ImportError as e:
    MODELS_AVAILABLE = False
    print(f"⚠️  模型导入失败: {e}")
    print("将使用通用模式处理数据")


class CustomUnpickler(pickle.Unpickler):
    """自定义Unpickler来处理模块路径问题"""

    def find_class(self, module, name):
        """重写find_class方法来处理模块路径映射"""
        # 处理model.model -> model的映射
        if module == 'model.model':
            module = 'model'

        # 处理__main__ -> model的映射（如果在主模块中定义的类）
        if module == '__main__' and name in ['Question', 'ExamQuestions']:
            module = 'model'

        try:
            return super().find_class(module, name)
        except (ImportError, AttributeError) as e:
            # 如果找不到模块，尝试从当前可用的模块中查找
            if MODELS_AVAILABLE and name in ['Question', 'ExamQuestions']:
                import model
                return getattr(model, name)
            raise e


class PKLReader:
    """PKL文件读取器类"""

    def __init__(self):
        self.data = None
        self.file_path = None

    def read_pkl(self, file_path: str) -> Any:
        """
        读取pkl文件，支持模块路径修复

        Args:
            file_path (str): pkl文件路径

        Returns:
            Any: 反序列化后的数据

        Raises:
            FileNotFoundError: 文件不存在
            pickle.UnpicklingError: 反序列化失败
        """
        file_path = Path(file_path)

        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")

        if not file_path.suffix.lower() in ['.pkl', '.pickle']:
            print(f"警告: 文件扩展名不是.pkl或.pickle: {file_path}")

        try:
            with open(file_path, 'rb') as f:
                # 使用自定义unpickler
                unpickler = CustomUnpickler(f)
                self.data = unpickler.load()
                self.file_path = file_path
                print(f"✅ 成功读取文件: {file_path}")
                return self.data
        except pickle.UnpicklingError as e:
            # 如果自定义unpickler失败，尝试标准方法
            print(f"⚠️  自定义加载失败，尝试标准方法: {e}")
            try:
                with open(file_path, 'rb') as f:
                    self.data = pickle.load(f)
                    self.file_path = file_path
                    print(f"✅ 使用标准方法成功读取文件: {file_path}")
                    return self.data
            except Exception as e2:
                raise pickle.UnpicklingError(f"反序列化失败: {e2}")
        except Exception as e:
            raise Exception(f"读取文件时发生错误: {e}")
    
    def get_data_info(self) -> dict:
        """
        获取数据的基本信息

        Returns:
            dict: 包含数据类型、大小等信息的字典
        """
        if self.data is None:
            return {"error": "没有加载数据"}

        info = {
            "数据类型": type(self.data).__name__,
            "文件路径": str(self.file_path),
            "文件大小": f"{self.file_path.stat().st_size} 字节" if self.file_path else "未知"
        }

        # 检查是否是模型数据
        if MODELS_AVAILABLE:
            if hasattr(self.data, '__class__') and self.data.__class__.__name__ == 'ExamQuestions':
                info["模型类型"] = "ExamQuestions (考试题目集合)"
                info["题目数量"] = len(self.data.questions) if hasattr(self.data, 'questions') else 0
                if hasattr(self.data, 'questions') and self.data.questions:
                    # 统计年份和区域分布
                    years = set()
                    regions = set()
                    for q in self.data.questions:
                        if hasattr(q, 'year') and q.year:
                            years.add(q.year)
                        if hasattr(q, 'region') and q.region:
                            regions.add(q.region)
                    info["涉及年份"] = sorted(list(years)) if years else ["未指定"]
                    info["涉及区域"] = sorted(list(regions)) if regions else ["未指定"]
                return info
            elif hasattr(self.data, '__class__') and self.data.__class__.__name__ == 'Question':
                info["模型类型"] = "Question (单个题目)"
                if hasattr(self.data, 'year'):
                    info["年份"] = self.data.year or "未指定"
                if hasattr(self.data, 'region'):
                    info["区域"] = self.data.region or "未指定"
                return info

        # 根据数据类型添加更多信息
        if isinstance(self.data, (list, tuple)):
            info["元素数量"] = len(self.data)
            if len(self.data) > 0:
                info["第一个元素类型"] = type(self.data[0]).__name__
                # 检查是否是Question列表
                if MODELS_AVAILABLE and hasattr(self.data[0], '__class__') and self.data[0].__class__.__name__ == 'Question':
                    info["内容类型"] = "Question对象列表"
        elif isinstance(self.data, dict):
            info["键数量"] = len(self.data)
            if self.data:
                info["键类型示例"] = type(list(self.data.keys())[0]).__name__
                info["值类型示例"] = type(list(self.data.values())[0]).__name__
        elif isinstance(self.data, str):
            info["字符串长度"] = len(self.data)
        elif hasattr(self.data, '__len__'):
            try:
                info["长度"] = len(self.data)
            except:
                pass

        return info
    
    def display_data(self, max_items: int = 10, max_depth: int = 2) -> None:
        """
        显示数据内容
        
        Args:
            max_items (int): 最大显示项目数
            max_depth (int): 最大显示深度
        """
        if self.data is None:
            print("❌ 没有加载数据")
            return
        
        print("\n" + "="*50)
        print("📊 数据内容预览:")
        print("="*50)
        
        self._display_recursive(self.data, max_items, max_depth, 0)
    
    def _display_recursive(self, obj: Any, max_items: int, max_depth: int, current_depth: int) -> None:
        """递归显示数据内容"""
        indent = "  " * current_depth
        
        if current_depth >= max_depth:
            print(f"{indent}... (达到最大深度)")
            return
        
        if isinstance(obj, dict):
            print(f"{indent}字典 (共{len(obj)}个键):")
            for i, (key, value) in enumerate(obj.items()):
                if i >= max_items:
                    print(f"{indent}  ... (还有{len(obj)-max_items}个项目)")
                    break
                print(f"{indent}  {key}: ", end="")
                if isinstance(value, (dict, list, tuple)) and current_depth < max_depth - 1:
                    print()
                    self._display_recursive(value, max_items, max_depth, current_depth + 2)
                else:
                    print(f"{type(value).__name__} = {str(value)[:100]}{'...' if len(str(value)) > 100 else ''}")
        
        elif isinstance(obj, (list, tuple)):
            type_name = "列表" if isinstance(obj, list) else "元组"
            print(f"{indent}{type_name} (共{len(obj)}个元素):")
            for i, item in enumerate(obj):
                if i >= max_items:
                    print(f"{indent}  ... (还有{len(obj)-max_items}个元素)")
                    break
                print(f"{indent}  [{i}]: ", end="")
                if isinstance(item, (dict, list, tuple)) and current_depth < max_depth - 1:
                    print()
                    self._display_recursive(item, max_items, max_depth, current_depth + 2)
                else:
                    print(f"{type(item).__name__} = {str(item)[:100]}{'...' if len(str(item)) > 100 else ''}")
        
        else:
            print(f"{indent}{type(obj).__name__}: {str(obj)[:200]}{'...' if len(str(obj)) > 200 else ''}")

    def display_questions(self, max_questions: int = 5) -> None:
        """
        专门显示Question和ExamQuestions数据

        Args:
            max_questions (int): 最大显示题目数量
        """
        if self.data is None:
            print("❌ 没有加载数据")
            return

        questions = []

        # 提取题目列表
        if MODELS_AVAILABLE:
            if hasattr(self.data, '__class__') and self.data.__class__.__name__ == 'ExamQuestions':
                questions = self.data.questions if hasattr(self.data, 'questions') else []
            elif hasattr(self.data, '__class__') and self.data.__class__.__name__ == 'Question':
                questions = [self.data]
            elif isinstance(self.data, list) and self.data and hasattr(self.data[0], '__class__') and self.data[0].__class__.__name__ == 'Question':
                questions = self.data

        if not questions:
            print("❌ 未找到Question格式的数据")
            return

        print("\n" + "="*60)
        print(f"📚 题目内容预览 (共{len(questions)}道题目):")
        print("="*60)

        for i, question in enumerate(questions[:max_questions]):
            print(f"\n📝 题目 {i+1}:")
            print("-" * 40)

            if hasattr(question, 'year') and question.year:
                print(f"📅 年份: {question.year}")
            if hasattr(question, 'region') and question.region:
                print(f"🌍 区域: {question.region}")

            if hasattr(question, 'content') and question.content:
                content = question.content.strip()
                print(f"❓ 题目: {content[:200]}{'...' if len(content) > 200 else ''}")

            if hasattr(question, 'answer') and question.answer:
                answer = question.answer.strip()
                print(f"✅ 答案: {answer[:100]}{'...' if len(answer) > 100 else ''}")

            if hasattr(question, 'analysis') and question.analysis:
                analysis = question.analysis.strip()
                print(f"💡 解析: {analysis[:150]}{'...' if len(analysis) > 150 else ''}")

        if len(questions) > max_questions:
            print(f"\n... 还有 {len(questions) - max_questions} 道题目未显示")

    def export_questions_to_json(self, output_path: str) -> None:
        """
        将题目数据导出为JSON格式

        Args:
            output_path (str): 输出文件路径
        """
        if self.data is None:
            print("❌ 没有加载数据")
            return

        questions_data = []

        # 提取题目数据
        if MODELS_AVAILABLE:
            if hasattr(self.data, '__class__') and self.data.__class__.__name__ == 'ExamQuestions':
                questions = self.data.questions if hasattr(self.data, 'questions') else []
            elif hasattr(self.data, '__class__') and self.data.__class__.__name__ == 'Question':
                questions = [self.data]
            elif isinstance(self.data, list) and self.data and hasattr(self.data[0], '__class__') and self.data[0].__class__.__name__ == 'Question':
                questions = self.data
            else:
                questions = []

            for question in questions:
                q_dict = {}
                if hasattr(question, 'year'):
                    q_dict['year'] = question.year
                if hasattr(question, 'region'):
                    q_dict['region'] = question.region
                if hasattr(question, 'content'):
                    q_dict['content'] = question.content
                if hasattr(question, 'answer'):
                    q_dict['answer'] = question.answer
                if hasattr(question, 'analysis'):
                    q_dict['analysis'] = question.analysis
                questions_data.append(q_dict)

        if not questions_data:
            print("❌ 未找到可导出的题目数据")
            return

        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(questions_data, f, ensure_ascii=False, indent=2)
            print(f"✅ 题目数据已导出到: {output_path}")
            print(f"📊 共导出 {len(questions_data)} 道题目")
        except Exception as e:
            print(f"❌ 导出失败: {e}")

    def save_as_text(self, output_path: str) -> None:
        """
        将数据保存为文本文件
        
        Args:
            output_path (str): 输出文件路径
        """
        if self.data is None:
            print("❌ 没有加载数据")
            return
        
        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(f"PKL文件内容导出\n")
                f.write(f"原文件: {self.file_path}\n")
                f.write(f"数据类型: {type(self.data).__name__}\n")
                f.write("="*50 + "\n\n")
                f.write(str(self.data))
            print(f"✅ 数据已保存到: {output_path}")
        except Exception as e:
            print(f"❌ 保存失败: {e}")


def main():
    """主函数 - 命令行接口"""
    parser = argparse.ArgumentParser(description='PKL文件读取工具 - 专门优化用于Question和ExamQuestions数据')
    parser.add_argument('file_path', help='PKL文件路径')
    parser.add_argument('--info', '-i', action='store_true', help='只显示文件信息')
    parser.add_argument('--questions', '-q', action='store_true', help='使用题目专用显示模式')
    parser.add_argument('--max-items', '-m', type=int, default=10, help='最大显示项目数 (默认: 10)')
    parser.add_argument('--max-depth', '-d', type=int, default=2, help='最大显示深度 (默认: 2)')
    parser.add_argument('--max-questions', type=int, default=5, help='最大显示题目数 (默认: 5)')
    parser.add_argument('--save-text', '-s', help='保存为文本文件的路径')
    parser.add_argument('--export-json', '-j', help='导出题目为JSON文件的路径')

    args = parser.parse_args()

    # 创建读取器实例
    reader = PKLReader()

    try:
        # 读取pkl文件
        reader.read_pkl(args.file_path)

        # 显示基本信息
        info = reader.get_data_info()
        print("\n📋 文件信息:")
        print("-" * 30)
        for key, value in info.items():
            print(f"{key}: {value}")

        # 根据模式显示内容
        if not args.info:
            # 检查是否应该使用题目模式
            is_question_data = False
            if MODELS_AVAILABLE and info.get("模型类型"):
                is_question_data = True

            if args.questions or is_question_data:
                # 使用题目专用显示
                reader.display_questions(args.max_questions)
            else:
                # 使用通用显示
                reader.display_data(args.max_items, args.max_depth)

        # 导出功能
        if args.export_json:
            reader.export_questions_to_json(args.export_json)

        if args.save_text:
            reader.save_as_text(args.save_text)

    except Exception as e:
        print(f"❌ 错误: {e}")
        sys.exit(1)


# 简单的使用示例函数
def example_usage():
    """使用示例"""
    print("PKL文件读取器使用示例 (专门优化用于Question和ExamQuestions数据):")
    print()
    print("1. 基本Python用法:")
    print("   reader = PKLReader()")
    print("   reader.read_pkl('questions.pkl')")
    print("   reader.display_questions()  # 题目专用显示")
    print("   reader.export_questions_to_json('output.json')  # 导出为JSON")
    print()
    print("2. 命令行用法:")
    print("   python pkl_reader.py questions.pkl  # 自动检测并使用题目模式")
    print("   python pkl_reader.py questions.pkl --info  # 只显示文件信息")
    print("   python pkl_reader.py questions.pkl --questions --max-questions 10  # 强制题目模式")
    print("   python pkl_reader.py questions.pkl --export-json output.json  # 导出JSON")
    print("   python pkl_reader.py questions.pkl --save-text output.txt  # 保存文本")
    print()
    print("3. 通用数据处理:")
    print("   python pkl_reader.py data.pkl --max-items 5 --max-depth 3")
    print()
    if MODELS_AVAILABLE:
        print("✅ 已检测到model.py，支持Question和ExamQuestions数据处理")
    else:
        print("⚠️  未检测到model.py，将使用通用模式")


if __name__ == "__main__":
    if len(sys.argv) == 1:
        example_usage()
    else:
        main()
