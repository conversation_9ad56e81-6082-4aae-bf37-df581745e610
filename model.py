from pydantic import BaseModel, Field




class Question(BaseModel):
    year: str = Field(description='年份', default='')
    region: str = Field(description='区域', default='')
    content: str = Field(description='题目', default='')
    answer: str = Field(description="题目答案", default="")
    analysis: str = Field(description="题目解析", default="")

class ExamQuestions(BaseModel):
    questions: list[Question] = Field(description="题目列表", default=[])