#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：创建示例pkl文件并测试读取
"""

import pickle
from model import Question, ExamQuestions

def create_test_data():
    """创建测试数据"""
    # 创建一些示例题目
    questions = [
        Question(
            year="2023",
            region="北京",
            content="下列哪个选项是正确的？\nA. 选项A\nB. 选项B\nC. 选项C\nD. 选项D",
            answer="C",
            analysis="这道题考查的是基础概念。正确答案是C，因为..."
        ),
        Question(
            year="2023",
            region="上海", 
            content="计算下列表达式的值：2 + 3 × 4",
            answer="14",
            analysis="根据运算顺序，先算乘法：3 × 4 = 12，再算加法：2 + 12 = 14"
        ),
        Question(
            year="2022",
            region="广东",
            content="简述Python中列表和元组的区别",
            answer="列表是可变的，元组是不可变的；列表用[]表示，元组用()表示",
            analysis="这是Python基础知识点。列表支持增删改操作，而元组一旦创建就不能修改"
        )
    ]
    
    # 创建ExamQuestions对象
    exam_questions = ExamQuestions(questions=questions)
    
    return questions, exam_questions

def save_test_files():
    """保存测试文件"""
    questions, exam_questions = create_test_data()
    
    # 保存单个Question对象
    with open('single_question.pkl', 'wb') as f:
        pickle.dump(questions[0], f)
    print("✅ 已创建 single_question.pkl")
    
    # 保存Question列表
    with open('questions_list.pkl', 'wb') as f:
        pickle.dump(questions, f)
    print("✅ 已创建 questions_list.pkl")
    
    # 保存ExamQuestions对象
    with open('exam_questions.pkl', 'wb') as f:
        pickle.dump(exam_questions, f)
    print("✅ 已创建 exam_questions.pkl")

def test_reader():
    """测试读取器"""
    from pkl_reader import PKLReader
    
    reader = PKLReader()
    
    print("\n" + "="*50)
    print("测试读取 exam_questions.pkl")
    print("="*50)
    
    try:
        reader.read_pkl('exam_questions.pkl')
        info = reader.get_data_info()
        print("\n📋 文件信息:")
        for key, value in info.items():
            print(f"{key}: {value}")
        
        print("\n使用题目专用显示:")
        reader.display_questions(max_questions=3)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    print("创建测试数据...")
    save_test_files()
    
    print("\n测试读取器...")
    test_reader()
